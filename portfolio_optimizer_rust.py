"""
Rust-based Portfolio Optimization Engine
High-performance replacement for scipy.optimize-based portfolio optimization
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Tuple, Optional, Any
import time

# Try to import the Rust module, fall back to original if not available
try:
    import ratio_calcs_rust
    RUST_OPTIMIZATION_AVAILABLE = True
    print("Rust portfolio optimization engine loaded successfully")
except ImportError:
    RUST_OPTIMIZATION_AVAILABLE = False
    print("Rust portfolio optimization engine not available, falling back to Python")
    # Import original functions as fallback
    from func_rest import process_combo as process_combo_python

from ratio_calcs_rust_wrapper import (
    portfolio_variance, neg_sharpe_ratio, neg_sortino_ratio, 
    compute_omega_ratio, compute_calmar_ratio, neg_modified_sharpe_ratio,
    calculate_var_cvar_numba
)
from func_mt5 import convert_log_to_arithmetic_returns

class RustPortfolioOptimizer:
    """
    High-performance Rust-based portfolio optimization engine
    """
    
    def __init__(self):
        self.rust_available = RUST_OPTIMIZATION_AVAILABLE
        self.optimization_stats = {
            'total_optimizations': 0,
            'rust_optimizations': 0,
            'python_fallbacks': 0,
            'total_time_saved': 0.0
        }
    
    def optimize_single_portfolio(
        self,
        mean_returns: np.ndarray,
        cov_matrix: np.ndarray,
        returns_matrix: np.ndarray,
        optimization_type: str,
        bounds: Tuple[float, float] = (-1.0, 1.0),
        max_iterations: int = 200
    ) -> Tuple[np.ndarray, bool, float]:
        """
        Optimize a single portfolio using Rust engine
        
        Args:
            mean_returns: Expected returns for each asset
            cov_matrix: Covariance matrix of returns
            returns_matrix: Historical returns matrix for advanced calculations
            optimization_type: 'min_variance', 'max_sharpe', or 'max_sortino'
            bounds: Weight bounds for each asset
            max_iterations: Maximum optimization iterations
            
        Returns:
            Tuple of (optimal_weights, success, objective_value)
        """
        if not self.rust_available:
            raise RuntimeError("Rust optimization engine not available")
        
        n_assets = len(mean_returns)
        bounds_lower = np.full(n_assets, bounds[0])
        bounds_upper = np.full(n_assets, bounds[1])
        
        try:
            start_time = time.time()
            
            weights, success, objective_value = ratio_calcs_rust.optimize_portfolio_rust(
                mean_returns.astype(np.float64),
                cov_matrix.astype(np.float64),
                returns_matrix.astype(np.float64),
                optimization_type,
                bounds_lower,
                bounds_upper,
                max_iterations
            )
            
            optimization_time = time.time() - start_time
            self.optimization_stats['total_optimizations'] += 1
            self.optimization_stats['rust_optimizations'] += 1
            
            return np.array(weights), success, objective_value
            
        except Exception as e:
            print(f"Rust optimization failed: {e}")
            self.optimization_stats['python_fallbacks'] += 1
            raise
    
    def batch_optimize_portfolios(
        self,
        combinations_data: List[Tuple[np.ndarray, np.ndarray, np.ndarray]],
        optimization_types: List[str] = ['min_variance', 'max_sharpe', 'max_sortino'],
        bounds: Tuple[float, float] = (-1.0, 1.0),
        max_iterations: int = 200
    ) -> List[List[Tuple[np.ndarray, bool, float, str]]]:
        """
        Batch optimize multiple portfolio combinations using Rust engine
        
        Args:
            combinations_data: List of (mean_returns, cov_matrix, returns_matrix) tuples
            optimization_types: List of optimization types to run
            bounds: Weight bounds for each asset
            max_iterations: Maximum optimization iterations
            
        Returns:
            List of optimization results for each combination
        """
        if not self.rust_available:
            raise RuntimeError("Rust optimization engine not available")
        
        # Convert data to the format expected by Rust
        rust_combinations_data = []
        for mean_returns, cov_matrix, returns_matrix in combinations_data:
            rust_combinations_data.append((
                mean_returns.tolist(),
                cov_matrix.tolist(),
                returns_matrix.tolist()
            ))
        
        n_assets = len(combinations_data[0][0]) if combinations_data else 0
        bounds_lower = np.full(n_assets, bounds[0])
        bounds_upper = np.full(n_assets, bounds[1])
        
        try:
            start_time = time.time()
            
            results = ratio_calcs_rust.batch_optimize_portfolios_rust(
                rust_combinations_data,
                optimization_types,
                bounds_lower,
                bounds_upper,
                max_iterations
            )
            
            optimization_time = time.time() - start_time
            self.optimization_stats['total_optimizations'] += len(combinations_data) * len(optimization_types)
            self.optimization_stats['rust_optimizations'] += len(combinations_data) * len(optimization_types)
            
            # Convert results back to numpy arrays
            converted_results = []
            for combo_results in results:
                converted_combo = []
                for weights, success, obj_val, opt_type in combo_results:
                    converted_combo.append((
                        np.array(weights),
                        success,
                        obj_val,
                        opt_type
                    ))
                converted_results.append(converted_combo)
            
            return converted_results
            
        except Exception as e:
            print(f"Rust batch optimization failed: {e}")
            self.optimization_stats['python_fallbacks'] += len(combinations_data) * len(optimization_types)
            raise

def normalize_weights(weights):
    """Normalize weights to sum to 1"""
    abs_sum = np.sum(np.abs(weights))
    if abs_sum > 1e-15:
        return weights / abs_sum
    return weights

def calculate_portfolio_metrics(weights, mean_returns, cov_matrix, adjusted_returns):
    """Calculate comprehensive portfolio metrics"""
    portfolio_return = np.dot(weights, mean_returns)
    portfolio_variance_val = portfolio_variance(weights, cov_matrix)
    portfolio_volatility = np.sqrt(portfolio_variance_val)
    
    # Calculate portfolio returns series
    portfolio_series = adjusted_returns.dot(weights)
    
    metrics = {
        'return': portfolio_return,
        'risk': portfolio_volatility,
        'variance': portfolio_variance_val,
    }
    
    # Calculate financial ratios
    if portfolio_volatility > 1e-15:
        metrics['sharpe'] = portfolio_return / portfolio_volatility
    else:
        metrics['sharpe'] = 0.0
    
    # Sortino ratio
    try:
        downside_returns = portfolio_series[portfolio_series < 0]
        if len(downside_returns) > 0:
            downside_std = np.std(downside_returns)
            if downside_std > 1e-15:
                metrics['sortino'] = portfolio_return / downside_std
            else:
                metrics['sortino'] = 0.0
        else:
            metrics['sortino'] = 0.0
    except:
        metrics['sortino'] = 0.0
    
    # Omega ratio
    try:
        metrics['omega'] = compute_omega_ratio(portfolio_series, 0.0)
    except:
        metrics['omega'] = 0.0
    
    # Calmar ratio
    try:
        metrics['calmar'] = compute_calmar_ratio(portfolio_series)
    except:
        metrics['calmar'] = 0.0
    
    # Modified Sharpe ratio
    try:
        metrics['mod_sharpe'] = -neg_modified_sharpe_ratio(
            weights, mean_returns, cov_matrix, adjusted_returns, 0.0
        )
    except:
        metrics['mod_sharpe'] = 0.0
    
    # CVaR
    try:
        if hasattr(portfolio_series, 'values'):
            values_array = portfolio_series.values.astype(np.float64)
        else:
            values_array = np.array(portfolio_series, dtype=np.float64)
        
        clean_values = values_array[~np.isnan(values_array)]
        if len(clean_values) > 0:
            _, cvar_95 = calculate_var_cvar_numba(clean_values, 0.95)
            metrics['cvar_95'] = abs(cvar_95)
        else:
            metrics['cvar_95'] = 0.0
    except:
        metrics['cvar_95'] = 0.0
    
    return metrics

def calculate_historical_metrics(portfolio_series):
    """Calculate historical performance metrics"""
    try:
        if isinstance(portfolio_series, pd.Series):
            values = portfolio_series.values
        else:
            values = np.array(portfolio_series)
        
        # Remove NaN values
        clean_values = values[~np.isnan(values)]
        
        if len(clean_values) == 0:
            return {}
        
        # Calculate cumulative returns
        cumulative_returns = np.cumsum(clean_values)
        
        # Calculate maximum drawdown
        running_max = np.maximum.accumulate(cumulative_returns)
        drawdowns = running_max - cumulative_returns
        max_drawdown = np.max(drawdowns) if len(drawdowns) > 0 else 0.0
        
        # Calculate other historical metrics
        total_return = cumulative_returns[-1] if len(cumulative_returns) > 0 else 0.0
        volatility = np.std(clean_values) if len(clean_values) > 1 else 0.0
        
        return {
            'total_return': total_return,
            'max_drawdown': max_drawdown,
            'historical_volatility': volatility,
            'periods': len(clean_values)
        }
        
    except Exception as e:
        print(f"Error calculating historical metrics: {e}")
        return {}

# Global optimizer instance
rust_optimizer = RustPortfolioOptimizer()

def process_combo_rust(combo_batch):
    """
    Rust-optimized version of process_combo function
    Replaces the scipy.optimize-based implementation with high-performance Rust
    """
    if not RUST_OPTIMIZATION_AVAILABLE:
        print("Rust optimization not available, falling back to Python implementation")
        return process_combo_python(combo_batch)

    import psutil
    import time

    current_usage = psutil.cpu_percent(interval=0.05)
    if current_usage > 80:
        time.sleep(0.05)

    batch_results = []

    # Pre-filter combinations to avoid expensive optimizations on poor candidates
    for combo_data in combo_batch:
        combo, current_df, historical_df = combo_data

        subset_returns = current_df[list(combo)]
        quick_mean = subset_returns.mean().mean()
        quick_std = subset_returns.std().mean()
        quick_sharpe = quick_mean / (quick_std + 1e-10)
        quick_downside = subset_returns[subset_returns < 0].std().mean() if (subset_returns < 0).any().any() else 1e-10
        quick_sortino = quick_mean / (quick_downside + 1e-10)
        threshold = 0
        quick_upside = ((subset_returns > threshold).sum() / subset_returns.count()).mean()
        quick_downside_omega = ((subset_returns < threshold).sum() / subset_returns.count()).mean()
        quick_omega = quick_upside / (quick_downside_omega + 1e-10)
        quick_cum_returns = subset_returns.mean(axis=1).cumsum()
        rolling_max = quick_cum_returns.expanding().max()
        quick_drawdowns = rolling_max - quick_cum_returns
        quick_max_dd = quick_drawdowns.max() if len(quick_drawdowns) > 0 else 1e-10
        quick_calmar = quick_mean / (quick_max_dd + 1e-10)
        quick_score = (
            (quick_sharpe > 0.05) * 1 +
            (quick_sortino > 0.05) * 1 +
            (quick_omega > 0.5) * 1 +
            (quick_calmar > 0.5) * 1
        )

        # Use the original combo directly (no inversion loop)
        adjusted = current_df[list(combo)]  # Keep log returns for chaining and calculations

        # Convert log returns to arithmetic returns for portfolio optimization (μ and Σ)
        arithmetic_returns = convert_log_to_arithmetic_returns(adjusted)
        cov_matrix = arithmetic_returns.cov().values
        mean_returns = arithmetic_returns.mean().values

        historical_subset = historical_df[list(combo)]
        display_combo = list(combo)  # No inversion-based sign change

        # Setup optimization parameters with bounds allowing long and short positions
        n_assets = len(combo)
        bounds = (-1.0, 1.0)  # Allow long and short positions

        # Check for invalid data
        if np.any(np.isnan(cov_matrix)) or np.any(np.isinf(cov_matrix)):
            continue
        if np.any(np.isnan(mean_returns)) or np.any(np.isinf(mean_returns)):
            continue

        # Prepare returns matrix for Rust optimization
        returns_matrix = adjusted.values.T  # Transpose to get assets x time_periods

        # Define optimization types to run
        optimization_configs = [
            ('min_variance', 'Min Variance', ['minvar', 'composite']),
            ('max_sharpe', 'Max Sharpe', ['maxsharpe', 'composite', 'ss_composite']),
            ('max_sortino', 'Max Sortino', ['maxsortino', 'ss_composite']),
        ]

        try:
            # Run optimizations using Rust engine
            for opt_type, opt_name, result_keys in optimization_configs:
                try:
                    weights, success, objective_value = rust_optimizer.optimize_single_portfolio(
                        mean_returns=mean_returns,
                        cov_matrix=cov_matrix,
                        returns_matrix=returns_matrix,
                        optimization_type=opt_type,
                        bounds=bounds,
                        max_iterations=200
                    )

                    if success:
                        # Normalize weights
                        weights = normalize_weights(weights)

                        # Check minimum weight threshold
                        if np.all(np.abs(weights) >= 0.05):
                            # Calculate comprehensive metrics
                            metrics = calculate_portfolio_metrics(weights, mean_returns, cov_matrix, adjusted)

                            # Calculate historical metrics if available
                            if len(historical_subset) > 0:
                                historical_portfolio = historical_subset.dot(weights)
                                historical_metrics = calculate_historical_metrics(historical_portfolio)
                                metrics.update(historical_metrics)

                            # Create candidate
                            candidate = {
                                'combo': display_combo,
                                'weights': weights.tolist(),
                                'optimization': opt_name,
                                **metrics
                            }

                            # Add to results with appropriate keys
                            for key in result_keys:
                                batch_results.append((key, candidate.copy()))

                except Exception as e:
                    print(f"Error in Rust optimization {opt_type}: {e}")
                    continue

            # Special handling for Max Omega and Max Calmar (using existing Python implementations for now)
            # These can be converted to Rust later
            try:
                # Max Omega Optimization (fallback to Python for now)
                init_guess = np.repeat(1/n_assets, n_assets)
                constraints = {'type': 'eq', 'fun': lambda x: np.sum(x) - 1}
                bounds_tuple = tuple((-1, 1) for _ in range(n_assets))

                import scipy.optimize as sco
                res_omega = sco.minimize(lambda w: -compute_omega_ratio(adjusted.dot(w), 0.0),
                                        init_guess,
                                        method='SLSQP', bounds=bounds_tuple, constraints=constraints,
                                        options={'ftol': 1e-8, 'maxiter': 200})
                if res_omega.success:
                    w_omega = normalize_weights(res_omega.x)
                    if np.all(np.abs(res_omega.x) >= 0.05):
                        metrics = calculate_portfolio_metrics(w_omega, mean_returns, cov_matrix, adjusted)
                        if len(historical_subset) > 0:
                            historical_portfolio = historical_subset.dot(w_omega)
                            historical_metrics = calculate_historical_metrics(historical_portfolio)
                            metrics.update(historical_metrics)
                        candidate = {
                            'combo': display_combo,
                            'weights': w_omega.tolist(),
                            'optimization': 'Max Omega',
                            **metrics
                        }
                        batch_results.append(('maxomega', candidate))
            except Exception as e:
                print(f"Error in Max Omega optimization: {e}")
                pass

            try:
                # Max Calmar Optimization (fallback to Python for now)
                res_calmar = sco.minimize(neg_calmar_ratio, init_guess, args=(mean_returns, cov_matrix, adjusted),
                                        method='SLSQP', bounds=bounds_tuple, constraints=constraints,
                                        options={'ftol': 1e-8, 'maxiter': 200})
                if res_calmar.success:
                    w_calmar = normalize_weights(res_calmar.x)
                    if np.all(np.abs(res_calmar.x) >= 0.05):
                        metrics = calculate_portfolio_metrics(w_calmar, mean_returns, cov_matrix, adjusted)
                        if len(historical_subset) > 0:
                            historical_portfolio = historical_subset.dot(w_calmar)
                            historical_metrics = calculate_historical_metrics(historical_portfolio)
                            metrics.update(historical_metrics)
                        candidate = {
                            'combo': display_combo,
                            'weights': w_calmar.tolist(),
                            'optimization': 'Max Calmar',
                            **metrics
                        }
                        batch_results.append(('maxcalmar', candidate))
            except Exception as e:
                print(f"Error in Max Calmar optimization: {e}")
                pass

            try:
                # Max Modified Sharpe Optimization (fallback to Python for now)
                res_mod = sco.minimize(neg_modified_sharpe_ratio, init_guess,
                                    args=(mean_returns, cov_matrix, adjusted, 0.0),
                                    method='SLSQP', bounds=bounds_tuple, constraints=constraints,
                                    options={'ftol': 1e-8, 'maxiter': 200})
                if res_mod.success:
                    w_mod = normalize_weights(res_mod.x)
                    if np.all(np.abs(res_mod.x) >= 0.05):
                        metrics_mod = calculate_portfolio_metrics(w_mod, mean_returns, cov_matrix, adjusted)
                        if len(historical_subset) > 0:
                            historical_portfolio = historical_subset.dot(w_mod)
                            historical_metrics = calculate_historical_metrics(historical_portfolio)
                            metrics_mod.update(historical_metrics)
                        candidate_mod = {
                            'combo': display_combo,
                            'weights': w_mod.tolist(),
                            'optimization': 'Max CF Sharpe',
                            **metrics_mod
                        }
                        batch_results.append(('maxmodsharpe', candidate_mod))
            except Exception as e:
                print(f"Error in Max Modified Sharpe optimization: {e}")
                pass

        except Exception as e:
            print(f"Error processing combination {combo}: {e}")
            continue

    return batch_results
