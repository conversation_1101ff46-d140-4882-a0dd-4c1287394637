"""
Pure Rust Parallel Portfolio Processing System
Replaces Python joblib with high-performance Rust parallel processing
"""

import numpy as np
import pandas as pd
from typing import List, Tuple, Dict, Any, Optional
import time
import psutil

try:
    from ratio_calcs_rust import parallel_process_portfolios_rust
    RUST_PARALLEL_AVAILABLE = True
    print("✅ Rust parallel processing engine loaded successfully")
except ImportError as e:
    print(f"⚠️  Rust parallel processing not available: {e}")
    RUST_PARALLEL_AVAILABLE = False


class RustParallelProcessor:
    """
    High-performance Rust-based parallel portfolio processor
    Replaces Python joblib multiprocessing with Rust Rayon parallel processing
    """
    
    def __init__(self):
        self.stats = {
            'total_combinations': 0,
            'processed_combinations': 0,
            'filtered_combinations': 0,
            'processing_time': 0.0,
            'rust_processing_time': 0.0
        }
    
    def process_combinations_parallel(
        self,
        combinations: List[List[str]],
        returns_df: pd.DataFrame,
        optimization_types: List[str] = None,
        bounds_lower: float = -1.0,
        bounds_upper: float = 1.0,
        max_iterations: int = 200,
        pre_filter_sharpe_threshold: float = 0.05,
        post_filter_negative_returns: bool = True,
        max_combinations: Optional[int] = None
    ) -> List[Dict[str, Any]]:
        """
        Process portfolio combinations using pure Rust parallel processing
        
        Args:
            combinations: List of symbol combinations to process
            returns_df: DataFrame with returns data
            optimization_types: List of optimization types to run
            bounds_lower: Lower bound for portfolio weights
            bounds_upper: Upper bound for portfolio weights
            max_iterations: Maximum optimization iterations
            pre_filter_sharpe_threshold: Minimum Sharpe ratio for pre-filtering
            post_filter_negative_returns: Whether to filter out negative return portfolios
            max_combinations: Maximum number of combinations to process
            
        Returns:
            List of optimized portfolio dictionaries
        """
        if not RUST_PARALLEL_AVAILABLE:
            raise RuntimeError("Rust parallel processing not available")
        
        start_time = time.time()
        
        # Limit combinations if specified
        if max_combinations and len(combinations) > max_combinations:
            combinations = combinations[:max_combinations]
        
        self.stats['total_combinations'] = len(combinations)
        
        # Default optimization types
        if optimization_types is None:
            optimization_types = ['max_sharpe', 'min_variance', 'max_sortino']
        
        # Prepare data for Rust processing in the expected format
        # The Rust function expects: combinations_data: Vec<(Vec<String>, Vec<Vec<f64>>, Vec<Vec<f64>>)>
        combinations_data = []
        returns_data = returns_df.values.tolist()  # Convert to list of lists

        for combo in combinations:
            # Extract data for this combination
            combo_indices = [returns_df.columns.get_loc(symbol) for symbol in combo if symbol in returns_df.columns]
            if not combo_indices:
                continue

            # Extract returns for this combination
            combo_returns = []
            for period_data in returns_data:
                combo_period = [period_data[i] for i in combo_indices]
                combo_returns.append(combo_period)

            # For now, use the same data for current and historical
            combinations_data.append((combo, combo_returns, combo_returns))

        print(f"🚀 Starting Rust parallel processing:")
        print(f"   Combinations: {len(combinations_data)}")
        print(f"   Optimization types: {optimization_types}")

        # Call Rust parallel processing function with correct signature
        rust_start_time = time.time()

        try:
            rust_results = parallel_process_portfolios_rust(
                combinations_data,
                optimization_types,
                np.array([bounds_lower]),  # Convert to numpy array
                np.array([bounds_upper]),  # Convert to numpy array
                max_iterations,
                pre_filter_sharpe_threshold,
                post_filter_negative_returns
            )
            
            rust_processing_time = time.time() - rust_start_time
            self.stats['rust_processing_time'] = rust_processing_time
            
            print(f"✅ Rust processing completed in {rust_processing_time:.3f}s")
            print(f"   Results: {len(rust_results)} portfolios")
            
        except Exception as e:
            print(f"❌ Rust parallel processing failed: {e}")
            raise
        
        # Convert Rust results to Python portfolio format
        portfolios = self._convert_rust_results_to_portfolios(rust_results)
        
        total_time = time.time() - start_time
        self.stats['processing_time'] = total_time
        self.stats['processed_combinations'] = len(portfolios)
        
        print(f"🎯 Parallel processing summary:")
        print(f"   Total time: {total_time:.3f}s")
        print(f"   Rust time: {rust_processing_time:.3f}s ({rust_processing_time/total_time*100:.1f}%)")
        print(f"   Portfolios generated: {len(portfolios)}")
        print(f"   Processing rate: {len(combinations)/total_time:.1f} combinations/sec")
        
        return portfolios
    
    def _convert_rust_results_to_portfolios(self, rust_results: List[str]) -> List[Dict[str, Any]]:
        """
        Convert Rust results to Python portfolio format

        Args:
            rust_results: List of strings from Rust processing (simplified format for now)

        Returns:
            List of portfolio dictionaries
        """
        portfolios = []

        for result_str in rust_results:
            try:
                # Parse the simplified result string format: "combo:optimization_type"
                if ':' in result_str:
                    combo_str, opt_type = result_str.split(':', 1)
                    symbols = combo_str.split(',')

                    # Create a simplified portfolio dictionary
                    # Note: This is a placeholder until we get full metrics from Rust
                    portfolio = {
                        'combo': symbols,
                        'weights': [1.0/len(symbols)] * len(symbols),  # Equal weights for now
                        'optimization': opt_type,
                        'return': 0.001,  # Placeholder
                        'risk': 0.01,  # Placeholder
                        'sharpe': 0.1,  # Placeholder
                        'sortino': 0.1,  # Placeholder
                        'omega': 1.0,  # Placeholder
                        'calmar': 0.1,  # Placeholder
                        'mod_sharpe': 0.1,  # Placeholder
                        'var_95': 0.0165,  # Placeholder
                        'cvar_95': 0.02,  # Placeholder
                        'max_drawdown': 0.01,  # Placeholder
                        'martin_ratio': 0.1,  # Placeholder
                    }

                    portfolios.append(portfolio)

            except Exception as e:
                print(f"⚠️  Error converting Rust result '{result_str}': {e}")
                continue

        return portfolios
    
    def get_processing_stats(self) -> Dict[str, Any]:
        """Get processing statistics"""
        return self.stats.copy()


def create_rust_parallel_processor() -> RustParallelProcessor:
    """Factory function to create a Rust parallel processor"""
    return RustParallelProcessor()


def get_system_info() -> Dict[str, Any]:
    """Get system information for parallel processing optimization"""
    return {
        'cpu_count': psutil.cpu_count(),
        'cpu_percent': psutil.cpu_percent(interval=0.1),
        'memory_available_gb': psutil.virtual_memory().available / (1024**3),
        'memory_percent': psutil.virtual_memory().percent,
        'rust_parallel_available': RUST_PARALLEL_AVAILABLE
    }


# Test function for development
def test_rust_parallel_processing():
    """Test the Rust parallel processing system"""
    if not RUST_PARALLEL_AVAILABLE:
        print("❌ Rust parallel processing not available for testing")
        return False
    
    print("🧪 Testing Rust parallel processing...")
    
    # Create test data
    np.random.seed(42)
    symbols = ['EURUSD', 'GBPUSD', 'USDJPY', 'AUDUSD']
    periods = 100
    returns_data = np.random.normal(0.0001, 0.01, (periods, len(symbols)))
    returns_df = pd.DataFrame(returns_data, columns=symbols)
    
    # Create test combinations
    from itertools import combinations as iter_combinations
    test_combinations = list(iter_combinations(symbols, 2))[:5]  # Test with 5 combinations
    
    # Create processor and test
    processor = create_rust_parallel_processor()
    
    try:
        results = processor.process_combinations_parallel(
            test_combinations,
            returns_df,
            optimization_types=['max_sharpe'],
            max_iterations=50
        )
        
        print(f"✅ Test successful: {len(results)} portfolios generated")
        print(f"📊 Stats: {processor.get_processing_stats()}")
        return True
        
    except Exception as e:
        print(f"❌ Test failed: {e}")
        return False


if __name__ == "__main__":
    # Run test when script is executed directly
    test_rust_parallel_processing()
